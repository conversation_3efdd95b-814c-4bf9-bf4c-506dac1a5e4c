import { z } from 'zod';

const envSchema = z.object({
    DATABASE_USERNAME: z.string().min(1),
    DATABASE_PASSWORD: z.string().min(1),
    DATABASE_HOST: z.string().min(1),
    DATABASE_PORT: z.coerce.number(),
    DATABASE_NAME: z.string().min(1)
});

const env = envSchema.safeParse(process.env);

if (!env.success)
{
    console.error(`Invalid environment variables on @repo/db: ${JSON.stringify(env.error)}`);
    process.exit(1);
}

export default env.data;
