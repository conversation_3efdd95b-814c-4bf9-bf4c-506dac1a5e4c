import js from '@eslint/js';
import { includeIgnoreFile } from '@eslint/compat';
import turboPlugin from 'eslint-plugin-turbo';
import tseslint from 'typescript-eslint';
import unicorn from 'eslint-plugin-unicorn';
import stylistic from '@stylistic/eslint-plugin';
import * as path from 'node:path';

/**
 * A shared ESLint configuration for the repository.
 *
 * @type {import('typescript-eslint').ConfigArray}
 * */
export const config = tseslint.config(
    includeIgnoreFile(path.join(import.meta.dirname, '../../.gitignore')),
    {ignores: ['**/*.json', '**/*.MD']},
    {
        files: ['**/*.js', '**/*.ts', '**/*.tsx'],
        extends: [
            js.configs.recommended,
            ...tseslint.configs.recommended,
            ...tseslint.configs.recommendedTypeChecked,
            ...tseslint.configs.stylisticTypeChecked
        ],
        plugins: {
            turbo: turboPlugin,
            unicorn
        },
        rules: {
            ...turboPlugin.configs.recommended.rules,
            '@typescript-eslint/no-unused-vars': [
                'error',
                {
                    argsIgnorePattern: '^_',
                    varsIgnorePattern: '^_'
                }
            ],
            '@typescript-eslint/consistent-type-imports': [
                'warn',
                {
                    prefer: 'type-imports',
                    fixStyle: 'separate-type-imports'
                }
            ],
            'unicorn/filename-case': [
                'error',
                {
                    case: 'kebabCase'
                }
            ]
        }
    },
    {
        files: ['**/*.js', '**/*.jsx', '**/*.mjs', '**/*.ts', '**/*.tsx'],
        plugins: {
            stylistic
        },
        rules: {
            curly: ['error'],
            'stylistic/brace-style': [
                'warn',
                'allman',
                {
                    allowSingleLine: false
                }
            ],
            'stylistic/comma-dangle': 'warn',
            'stylistic/comma-spacing': [
                'error',
                {
                    before: false,
                    after: true
                }
            ],
            'stylistic/function-call-spacing': 'error',
            'stylistic/indent': ['warn', 4],
            'stylistic/keyword-spacing': [
                'error',
                {
                    before: true,
                    after: true
                }
            ],
            'stylistic/lines-between-class-members': [
                'warn',
                {
                    enforce: [
                        {
                            blankLine: 'always',
                            prev: 'field',
                            next: 'method'
                        },
                        {
                            blankLine: 'always',
                            prev: 'method',
                            next: 'method'
                        }
                    ]
                }
            ],
            'stylistic/member-delimiter-style': [
                'warn',
                {
                    multiline: {
                        delimiter: 'semi',
                        requireLast: true
                    },
                    singleline: {
                        delimiter: 'semi',
                        requireLast: false
                    }
                }
            ],
            'stylistic/no-extra-parens': [
                'warn',
                'all'
            ],
            'stylistic/no-extra-semi': 'warn',
            'stylistic/no-multiple-empty-lines': [
                'warn',
                {
                    max: 1,
                    maxBOF: 0,
                    maxEOF: 0
                }
            ],
            'stylistic/no-trailing-spaces': [
                'warn',
                {
                    skipBlankLines: true
                }
            ],
            'stylistic/padding-line-between-statements': [
                'error',
                {
                    blankLine: 'always',
                    prev: '*',
                    next: 'return'
                }
            ],
            'stylistic/quotes': [
                'warn',
                'single',
                {
                    avoidEscape: true,
                    allowTemplateLiterals: false
                }
            ],
            'stylistic/quote-props': [
                'warn',
                'as-needed'
            ],
            'stylistic/semi': [
                'warn',
                'always',
                {
                    omitLastInOneLineBlock: false
                }
            ],
            'stylistic/space-before-function-paren': [
                'warn',
                {
                    anonymous: 'never',
                    named: 'never',
                    asyncArrow: 'always'
                }
            ],
            'stylistic/type-annotation-spacing': 'error'
        }
    },
    {
        linterOptions: {
            reportUnusedDisableDirectives: true
        },
        languageOptions: {
            parserOptions: {
                projectService: true
            }
        }
    }
);
