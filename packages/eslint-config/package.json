{"name": "@repo/eslint-config", "version": "0.0.0", "type": "module", "private": true, "scripts": {"clean": "git clean -xdf node_modules"}, "exports": {"./base": "./base.js"}, "author": "<PERSON>", "dependencies": {"@eslint/compat": "^1.2.9", "@eslint/js": "^9.30.0", "@stylistic/eslint-plugin": "^5.1.0", "eslint-plugin-turbo": "^2.5.0", "eslint-plugin-unicorn": "^59.0.1", "typescript-eslint": "^8.35.0"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "^22.16.3", "eslint": "catalog:", "typescript": "catalog:"}}