{"name": "@repo/db", "version": "0.0.0", "type": "module", "exports": {"./client": {"types": "./dist/client.d.ts", "default": "./dist/client.js"}, "./schema": {"types": "./dist/schema.d.ts", "default": "./dist/schema.js"}}, "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "push": "drizzle-kit push", "studio": "drizzle-kit studio"}, "author": "<PERSON>", "private": true, "packageManager": "pnpm@10.13.1", "dependencies": {"drizzle-orm": "^0.44.3", "pg": "^8.16.3", "zod": "catalog:"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/pg": "^8.15.4", "drizzle-kit": "^0.31.4", "eslint": "catalog:", "typescript": "catalog:"}}