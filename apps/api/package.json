{"name": "bookmark-plus-api", "version": "0.0.0", "type": "module", "private": true, "scripts": {"build": "tsc", "check-types": "tsc --noEmit", "clean": "git clean -xdf .turbo dist node_modules", "dev": "tsx watch src/index.ts", "lint": "eslint .", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON>", "packageManager": "pnpm@10.13.1", "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/rate-limit": "^10.3.0", "@repo/trpc": "workspace:*", "@trpc/server": "^11.4.3", "fastify": "^5.4.0"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.16.3", "eslint": "catalog:", "tsx": "^4.20.3", "typescript": "catalog:"}}